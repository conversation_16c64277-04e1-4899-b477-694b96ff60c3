#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应矩形检测算法 - 基于MaixPy官方API (稳定版)
适用于2米距离内的矩形识别，使用官方函数库调用
修复内存安全问题，简化图像处理流程
"""

from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
from micu_uart_lib import SimpleUART, micu_printf

# 全局配置参数
DISTANCE_CONFIGS = {
    "close": {      # 0.5-1.0米
        "min_area": 500,
        "max_area": 8000,
        "epsilon_factor": 0.02
    },
    "medium": {     # 1.0-1.5米
        "min_area": 200,
        "max_area": 2000,
        "epsilon_factor": 0.025
    },
    "far": {        # 1.5-2.0米
        "min_area": 100,
        "max_area": 600,
        "epsilon_factor": 0.03
    }
}

# 自适应阈值参数
BASE_THRESHOLD = 46
BRIGHTNESS_LOW = 80
BRIGHTNESS_HIGH = 150
        
def calculate_image_brightness(img_cv):
    """
    计算图像平均亮度

    Args:
        img_cv: OpenCV格式图像

    Returns:
        float: 平均亮度值
    """
    try:
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        return np.mean(gray)
    except Exception as e:
        print(f"计算亮度错误: {e}")
        return 128  # 返回默认值

def get_adaptive_threshold(brightness):
    """
    根据图像亮度获取自适应二值化阈值

    Args:
        brightness: 图像平均亮度

    Returns:
        int: 二值化阈值
    """
    if brightness < BRIGHTNESS_LOW:      # 暗环境
        return 35
    elif brightness > BRIGHTNESS_HIGH:   # 亮环境
        return 60
    else:                               # 正常环境
        return BASE_THRESHOLD

def estimate_distance_category(contour_area):
    """
    根据轮廓面积估算距离类别

    Args:
        contour_area: 轮廓面积

    Returns:
        str: 距离类别 ("close", "medium", "far")
    """
    if contour_area > 2000:
        return "close"
    elif contour_area > 600:
        return "medium"
    else:
        return "far"
    
def detect_rectangles_safe(img):
    """
    安全的矩形检测函数

    Args:
        img: maix.image.Image对象

    Returns:
        list: 检测到的矩形列表 [(approx, area, distance_category), ...]
    """
    try:
        # 安全地转换为OpenCV格式
        img_cv = image.image2cv(img, ensure_bgr=True, copy=True)

        # 计算图像亮度
        brightness = calculate_image_brightness(img_cv)

        # 获取自适应阈值
        adaptive_threshold = get_adaptive_threshold(brightness)

        # 转换为灰度图
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

        # 应用自适应二值化
        _, binary = cv2.threshold(gray, adaptive_threshold, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        detected_rectangles = []

        # 多尺度检测
        for distance_category, config in DISTANCE_CONFIGS.items():
            min_area = config["min_area"]
            max_area = config["max_area"]
            epsilon_factor = config["epsilon_factor"]

            for cnt in contours:
                try:
                    area = cv2.contourArea(cnt)

                    # 面积筛选
                    if min_area < area < max_area:
                        # 多边形近似
                        epsilon = epsilon_factor * cv2.arcLength(cnt, True)
                        approx = cv2.approxPolyDP(cnt, epsilon, True)

                        # 检查是否为四边形
                        if len(approx) == 4:
                            detected_rectangles.append((approx, area, distance_category))
                except Exception as e:
                    print(f"轮廓处理错误: {e}")
                    continue

        # 简化去重逻辑
        if detected_rectangles:
            # 按面积降序排序，只保留最大的矩形
            detected_rectangles.sort(key=lambda x: x[1], reverse=True)
            return [detected_rectangles[0]]  # 只返回最大的矩形

        return []

    except Exception as e:
        print(f"矩形检测错误: {e}")
        return []
    
def draw_detection_results_safe(img, rectangles, brightness, threshold):
    """
    安全地绘制检测结果

    Args:
        img: maix.image.Image对象
        rectangles: 检测到的矩形列表
        brightness: 图像亮度
        threshold: 使用的阈值

    Returns:
        处理后的图像
    """
    try:
        # 安全地转换为OpenCV格式进行绘制
        img_cv = image.image2cv(img, ensure_bgr=True, copy=True)

        for i, (approx, area, category) in enumerate(rectangles):
            try:
                # 根据距离类别选择颜色
                if category == "close":
                    color = (0, 255, 0)    # 绿色
                elif category == "medium":
                    color = (255, 255, 0)  # 黄色
                else:
                    color = (0, 0, 255)    # 红色

                # 绘制矩形轮廓
                cv2.drawContours(img_cv, [approx], -1, color, 2)

                # 计算中心点
                M = cv2.moments(approx)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])

                    # 绘制中心点
                    cv2.circle(img_cv, (cx, cy), 5, color, -1)

                    # 绘制信息文本
                    info_text = f"{category}:{int(area)}"
                    cv2.putText(img_cv, info_text, (cx-30, cy-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            except Exception as e:
                print(f"绘制矩形错误: {e}")
                continue

        # 显示调试信息
        debug_text = f"B:{brightness:.0f} T:{threshold}"
        cv2.putText(img_cv, debug_text, (5, 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 转换回maix.image格式
        return image.cv2image(img_cv, bgr=True, copy=True)

    except Exception as e:
        print(f"绘制结果错误: {e}")
        return img  # 返回原图

def detect_purple_laser_safe(img):
    """
    安全的紫色激光检测函数

    Args:
        img: maix.image.Image对象

    Returns:
        tuple: (激光点列表)
    """
    try:
        # 安全地转换为OpenCV格式
        img_cv = image.image2cv(img, ensure_bgr=True, copy=True)

        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)

        # 简化的紫色检测范围
        lower_purple = np.array([125, 60, 60])
        upper_purple = np.array([165, 255, 255])

        # 创建掩码
        mask = cv2.inRange(hsv, lower_purple, upper_purple)

        # 简单的形态学处理
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        laser_points = []
        for cnt in contours:
            try:
                # 过滤小轮廓
                if cv2.contourArea(cnt) > 5:
                    rect = cv2.minAreaRect(cnt)
                    cx, cy = map(int, rect[0])
                    laser_points.append((cx, cy))
            except Exception as e:
                print(f"激光点处理错误: {e}")
                continue

        return laser_points

    except Exception as e:
        print(f"激光检测错误: {e}")
        return []

def main():
    """主程序 - 简化版本"""
    try:
        # 初始化设备
        print("初始化设备...")
        disp = display.Display()
        cam = camera.Camera(180, 120, image.Format.FMT_BGR888)

        # 初始化串口
        uart = SimpleUART()
        if uart.init("/dev/ttyS0", 115200, set_as_global=True):
            print("串口初始化成功")
            uart.set_frame("$$", "##", True)
        else:
            print("串口初始化失败")
            return

        # FPS计算
        fps = 0
        last_time = time.ticks_ms()

        print("自适应矩形检测器启动...")

        while not app.need_exit():
            try:
                # 计算FPS
                current_time = time.ticks_ms()
                if current_time - last_time > 0:
                    fps = 1000.0 / (current_time - last_time)
                last_time = current_time

                # 读取图像
                img = cam.read()
                if img is None:
                    continue

                # 矩形检测
                rectangles = detect_rectangles_safe(img)

                # 激光检测
                laser_points = detect_purple_laser_safe(img)

                # 计算亮度和阈值用于显示
                img_cv = image.image2cv(img, ensure_bgr=True, copy=True)
                brightness = calculate_image_brightness(img_cv)
                threshold = get_adaptive_threshold(brightness)

                # 绘制检测结果
                img_result = draw_detection_results_safe(img, rectangles, brightness, threshold)

                # 串口数据发送
                if rectangles:
                    try:
                        # 发送矩形数据
                        rect_data = f"R,{len(rectangles)}"
                        for approx, area, category in rectangles:
                            # 计算中心点
                            M = cv2.moments(approx)
                            if M["m00"] != 0:
                                cx = int(M["m10"] / M["m00"])
                                cy = int(M["m01"] / M["m00"])
                                rect_data += f",{cx},{cy},{int(area)},{category}"
                        micu_printf(rect_data)
                    except Exception as e:
                        print(f"发送矩形数据错误: {e}")

                if laser_points:
                    try:
                        # 发送激光点数据
                        laser_data = f"L,{len(laser_points)}"
                        for (x, y) in laser_points:
                            laser_data += f",{x},{y}"
                        micu_printf(laser_data)
                    except Exception as e:
                        print(f"发送激光数据错误: {e}")

                # 显示FPS
                try:
                    img_cv = image.image2cv(img_result, ensure_bgr=True, copy=True)
                    cv2.putText(img_cv, f"FPS: {fps:.1f}", (5, 35),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

                    # 显示图像
                    img_show = image.cv2image(img_cv, bgr=True, copy=True)
                    disp.show(img_show)
                except Exception as e:
                    print(f"显示图像错误: {e}")
                    # 如果显示失败，至少显示原图
                    disp.show(img)

            except Exception as e:
                print(f"主循环错误: {e}")
                time.sleep_ms(100)  # 短暂延迟后继续
                continue

    except Exception as e:
        print(f"程序初始化错误: {e}")
    finally:
        print("程序退出")

if __name__ == "__main__":
    main()
