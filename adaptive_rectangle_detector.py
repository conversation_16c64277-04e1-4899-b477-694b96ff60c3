#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应矩形检测算法 - 基于MaixPy官方API
适用于2米距离内的矩形识别，使用官方函数库调用
"""

from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
from micu_uart_lib import SimpleUART, micu_printf

class AdaptiveRectangleDetector:
    """自适应矩形检测器类"""
    
    def __init__(self, camera_width=180, camera_height=120):
        """
        初始化自适应矩形检测器
        
        Args:
            camera_width: 摄像头宽度
            camera_height: 摄像头高度
        """
        self.camera_width = camera_width
        self.camera_height = camera_height
        
        # 基础参数配置
        self.base_min_area = 100      # 2米距离基准最小面积
        self.base_max_area = 600      # 2米距离基准最大面积
        self.epsilon_factor = 0.025   # 多边形近似系数
        
        # 距离配置表
        self.distance_configs = {
            "close": {      # 0.5-1.0米
                "min_area": 500,
                "max_area": 8000,
                "epsilon_factor": 0.02,
                "threshold_offset": 0
            },
            "medium": {     # 1.0-1.5米  
                "min_area": 200,
                "max_area": 2000,
                "epsilon_factor": 0.025,
                "threshold_offset": 4
            },
            "far": {        # 1.5-2.0米
                "min_area": 100,
                "max_area": 600,
                "epsilon_factor": 0.03,
                "threshold_offset": 9
            }
        }
        
        # 自适应阈值参数
        self.base_threshold = 46
        self.brightness_low = 80
        self.brightness_high = 150
        
    def calculate_image_brightness(self, img_cv):
        """
        计算图像平均亮度
        
        Args:
            img_cv: OpenCV格式图像
            
        Returns:
            float: 平均亮度值
        """
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        return np.mean(gray)
    
    def get_adaptive_threshold(self, brightness):
        """
        根据图像亮度获取自适应二值化阈值
        
        Args:
            brightness: 图像平均亮度
            
        Returns:
            int: 二值化阈值
        """
        if brightness < self.brightness_low:      # 暗环境
            return 35
        elif brightness > self.brightness_high:   # 亮环境  
            return 60
        else:                                     # 正常环境
            return self.base_threshold
    
    def estimate_distance_category(self, contour_area):
        """
        根据轮廓面积估算距离类别
        
        Args:
            contour_area: 轮廓面积
            
        Returns:
            str: 距离类别 ("close", "medium", "far")
        """
        if contour_area > 2000:
            return "close"
        elif contour_area > 600:
            return "medium"
        else:
            return "far"
    
    def detect_rectangles_multi_scale(self, img):
        """
        多尺度矩形检测
        
        Args:
            img: maix.image.Image对象
            
        Returns:
            list: 检测到的矩形列表 [(approx, area, distance_category), ...]
        """
        # 转换为OpenCV格式
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        
        # 计算图像亮度
        brightness = self.calculate_image_brightness(img_cv)
        
        # 获取自适应阈值
        adaptive_threshold = self.get_adaptive_threshold(brightness)
        
        # 转换为灰度图
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # 应用自适应二值化
        _, binary = cv2.threshold(gray, adaptive_threshold, 255, cv2.THRESH_BINARY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        detected_rectangles = []
        
        # 多尺度检测
        for distance_category, config in self.distance_configs.items():
            min_area = config["min_area"]
            max_area = config["max_area"]
            epsilon_factor = config["epsilon_factor"]
            
            for cnt in contours:
                area = cv2.contourArea(cnt)
                
                # 面积筛选
                if min_area < area < max_area:
                    # 多边形近似
                    epsilon = epsilon_factor * cv2.arcLength(cnt, True)
                    approx = cv2.approxPolyDP(cnt, epsilon, True)
                    
                    # 检查是否为四边形
                    if len(approx) == 4:
                        detected_rectangles.append((approx, area, distance_category))
        
        # 去重：如果同一个轮廓被多个尺度检测到，保留面积最大的
        unique_rectangles = []
        processed_areas = set()
        
        # 按面积降序排序
        detected_rectangles.sort(key=lambda x: x[1], reverse=True)
        
        for rect_data in detected_rectangles:
            approx, area, category = rect_data
            
            # 检查是否与已处理的矩形重叠
            is_duplicate = False
            for processed_area in processed_areas:
                if abs(area - processed_area) < area * 0.1:  # 10%容差
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_rectangles.append(rect_data)
                processed_areas.add(area)
        
        return unique_rectangles
    
    def draw_detection_results(self, img, rectangles, brightness, threshold):
        """
        绘制检测结果
        
        Args:
            img: maix.image.Image对象
            rectangles: 检测到的矩形列表
            brightness: 图像亮度
            threshold: 使用的阈值
        """
        # 转换为OpenCV格式进行绘制
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        
        for i, (approx, area, category) in enumerate(rectangles):
            # 根据距离类别选择颜色
            if category == "close":
                color = (0, 255, 0)    # 绿色
            elif category == "medium":
                color = (255, 255, 0)  # 黄色
            else:
                color = (0, 0, 255)    # 红色
            
            # 绘制矩形轮廓
            cv2.drawContours(img_cv, [approx], -1, color, 2)
            
            # 计算中心点
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # 绘制中心点
                cv2.circle(img_cv, (cx, cy), 5, color, -1)
                
                # 绘制信息文本
                info_text = f"{category}:{area}"
                cv2.putText(img_cv, info_text, (cx-30, cy-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # 显示调试信息
        debug_text = f"Bright:{brightness:.1f} Thresh:{threshold}"
        cv2.putText(img_cv, debug_text, (5, 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        # 转换回maix.image格式
        return image.cv2image(img_cv, bgr=True, copy=False)

class PurpleLaserDetector:
    """优化的紫色激光检测器"""
    
    def __init__(self):
        # 扩大HSV检测范围以提高鲁棒性
        self.hsv_ranges = [
            # 主紫色范围
            {"lower": np.array([125, 60, 60]), "upper": np.array([165, 255, 255])},
            # 偏蓝紫色
            {"lower": np.array([110, 50, 50]), "upper": np.array([130, 255, 255])},
            # 偏红紫色  
            {"lower": np.array([160, 50, 50]), "upper": np.array([180, 255, 255])}
        ]
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        """
        检测紫色激光点
        
        Args:
            img: maix.image.Image对象
            
        Returns:
            tuple: (处理后的图像, 激光点列表)
        """
        # 转换为OpenCV格式
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
        
        # 多范围检测
        combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        for range_config in self.hsv_ranges:
            mask = cv2.inRange(hsv, range_config["lower"], range_config["upper"])
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 形态学处理
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, self.kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, self.kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        laser_points = []
        for cnt in contours:
            # 过滤小轮廓
            if cv2.contourArea(cnt) > 5:
                rect = cv2.minAreaRect(cnt)
                cx, cy = map(int, rect[0])
                laser_points.append((cx, cy))
                
                # 绘制激光点
                cv2.circle(img_cv, (cx, cy), 3, (255, 0, 255), -1)
                cv2.putText(img_cv, "Laser", (cx-20, cy-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        # 转换回maix.image格式
        result_img = image.cv2image(img_cv, bgr=True, copy=False)
        return result_img, laser_points

def main():
    """主程序"""
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(180, 120, image.Format.FMT_BGR888)
    
    # 初始化检测器
    rect_detector = AdaptiveRectangleDetector(180, 120)
    laser_detector = PurpleLaserDetector()
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)
    else:
        print("串口初始化失败")
        return
    
    # FPS计算
    fps = 0
    last_time = time.ticks_ms()
    
    print("自适应矩形检测器启动...")
    
    while not app.need_exit():
        # 计算FPS
        current_time = time.ticks_ms()
        if current_time - last_time > 0:
            fps = 1000.0 / (current_time - last_time)
        last_time = current_time
        
        # 读取图像
        img = cam.read()
        
        # 矩形检测
        rectangles = rect_detector.detect_rectangles_multi_scale(img)
        
        # 激光检测
        img, laser_points = laser_detector.detect(img)
        
        # 绘制检测结果
        brightness = rect_detector.calculate_image_brightness(
            image.image2cv(img, ensure_bgr=True, copy=False)
        )
        threshold = rect_detector.get_adaptive_threshold(brightness)
        img_result = rect_detector.draw_detection_results(img, rectangles, brightness, threshold)
        
        # 串口数据发送
        if rectangles:
            # 发送矩形数据
            rect_data = f"R,{len(rectangles)}"
            for approx, area, category in rectangles:
                # 计算中心点
                M = cv2.moments(approx)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    rect_data += f",{cx},{cy},{area},{category}"
            micu_printf(rect_data)
        
        if laser_points:
            # 发送激光点数据
            laser_data = f"L,{len(laser_points)}"
            for (x, y) in laser_points:
                laser_data += f",{x},{y}"
            micu_printf(laser_data)
        
        # 显示FPS
        img_cv = image.image2cv(img_result, ensure_bgr=True, copy=False)
        cv2.putText(img_cv, f"FPS: {fps:.1f}", (5, 35),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        # 显示图像
        img_show = image.cv2image(img_cv, bgr=True, copy=False)
        disp.show(img_show)

if __name__ == "__main__":
    main()
