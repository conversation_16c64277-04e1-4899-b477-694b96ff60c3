from maix import image, display, app, time, camera
import cv2

# 初始化显示和摄像头
disp = display.Display()
cam = camera.Camera(640, 480, image.Format.FMT_BGR888)

# 定义参数
min_contour_area = 30000
max_contour_area = 250000
target_sides = 4


while not app.need_exit():
    # 从摄像头读取图像
    img = cam.read()
    img = image.image2cv(img, ensure_bgr=True, copy=False)
    # 转灰度并二值化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)

    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    quads = []
    for cnt in contours:
        area = cv2.contourArea(cnt)
        
        if min_contour_area < area < max_contour_area:
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) == target_sides:
                quads.append((approx, area))
                print(area)

    # 按面积降序排列：第一个为最大外框，其余为内框
    quads.sort(key=lambda x: x[1], reverse=True)

    # 绘制轮廓
    output = img.copy()
    for i, (approx, area) in enumerate(quads):
        color = (0, 0, 255) if i == 0 else (0, 255, 0)
        cv2.drawContours(output, [approx], -1, color, 2)

    # 转回 maix.image.Image 并显示
    img_show = image.cv2image(output, bgr=True, copy=False)
    disp.show(img_show)
