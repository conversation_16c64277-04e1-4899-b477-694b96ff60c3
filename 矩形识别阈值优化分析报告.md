# 矩形识别阈值深度优化分析报告

## 项目概述
基于MaixCAM的矩形识别系统，要求在2米距离内稳定识别矩形目标。当前系统存在阈值设置不当导致远距离识别失败的问题。

## 当前系统分析

### 1. 硬件配置
- **摄像头分辨率**: 180×120 (main.py) / 640×480 (test.py)
- **图像格式**: BGR888
- **处理平台**: MaixCAM

### 2. 当前阈值设置问题

#### 2.1 轮廓面积阈值问题
```python
# 当前设置 (main.py)
min_contour_area = 1000    # 过大！
max_contour_area = 10000   # 过大！

# 测试文件设置 (test.py)  
min_contour_area = 30000   # 更大！
max_contour_area = 250000  # 更大！
```

**问题分析**：
- 180×120分辨率下，2米距离的20cm×15cm矩形约占252像素面积
- 当前最小阈值1000远大于实际目标面积
- 导致远距离目标被过滤掉

#### 2.2 二值化阈值问题
```python
_, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)
```

**问题分析**：
- 固定阈值46无法适应不同光照条件
- 室外强光/室内弱光下识别效果差异巨大

#### 2.3 多边形近似精度问题
```python
epsilon = 0.03 * cv2.arcLength(cnt, True)
```

**问题分析**：
- 3%的近似精度在远距离小目标上可能过于严格
- 可能导致噪声干扰下的矩形被误判

## 距离-面积关系数学模型

### 3.1 理论计算
假设目标矩形实际尺寸：20cm × 15cm

**视场角计算**：
- MaixCAM典型视场角：约60°水平
- 180像素对应60°视场

**不同距离下的像素尺寸**：

| 距离(m) | 水平像素 | 垂直像素 | 面积(像素) | 推荐min_area | 推荐max_area |
|---------|----------|----------|------------|--------------|--------------|
| 0.5     | 72       | 54       | 3888       | 2000         | 8000         |
| 1.0     | 36       | 27       | 972        | 500          | 2000         |
| 1.5     | 24       | 18       | 432        | 200          | 1000         |
| 2.0     | 18       | 14       | 252        | 100          | 600          |

### 3.2 自适应面积阈值公式
```python
def calculate_area_thresholds(distance_m, target_size_cm=(20, 15)):
    """根据距离计算面积阈值"""
    # 基准：1米距离下的像素尺寸
    base_pixels_per_cm = 1.8  # 180像素/100cm视场
    
    # 距离平方反比关系
    pixels_per_cm = base_pixels_per_cm / distance_m
    target_area = (target_size_cm[0] * pixels_per_cm) * (target_size_cm[1] * pixels_per_cm)
    
    min_area = int(target_area * 0.4)  # 40%容错
    max_area = int(target_area * 2.5)  # 250%上限
    
    return min_area, max_area
```

## 优化阈值设置方案

### 4.1 动态二值化阈值
```python
def adaptive_threshold(gray_img):
    """自适应二值化"""
    # 方案1：OTSU自动阈值
    _, binary1 = cv2.threshold(gray_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 方案2：自适应阈值
    binary2 = cv2.adaptiveThreshold(gray_img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    
    # 方案3：基于图像亮度的动态阈值
    mean_brightness = np.mean(gray_img)
    if mean_brightness < 80:      # 暗环境
        threshold = 35
    elif mean_brightness > 150:   # 亮环境  
        threshold = 60
    else:                         # 正常环境
        threshold = 46
    
    _, binary3 = cv2.threshold(gray_img, threshold, 255, cv2.THRESH_BINARY)
    
    return binary3  # 推荐使用方案3
```

### 4.2 优化的轮廓检测参数
```python
# 2米内识别的推荐设置
DISTANCE_CONFIGS = {
    "close": {      # 0.5-1.0米
        "min_area": 500,
        "max_area": 8000,
        "epsilon_factor": 0.02,
        "threshold_base": 46
    },
    "medium": {     # 1.0-1.5米  
        "min_area": 200,
        "max_area": 2000,
        "epsilon_factor": 0.025,
        "threshold_base": 50
    },
    "far": {        # 1.5-2.0米
        "min_area": 100,
        "max_area": 600,
        "epsilon_factor": 0.03,
        "threshold_base": 55
    }
}
```

### 4.3 激光检测HSV优化
```python
class OptimizedPurpleLaserDetector:
    def __init__(self):
        # 扩大HSV检测范围
        self.hsv_ranges = [
            # 主紫色范围
            {"lower": np.array([125, 60, 60]), "upper": np.array([165, 255, 255])},
            # 偏蓝紫色
            {"lower": np.array([110, 50, 50]), "upper": np.array([130, 255, 255])},
            # 偏红紫色  
            {"lower": np.array([160, 50, 50]), "upper": np.array([180, 255, 255])}
        ]
        
    def detect_adaptive(self, img):
        """自适应激光检测"""
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        
        # 多范围检测
        for range_config in self.hsv_ranges:
            mask = cv2.inRange(hsv, range_config["lower"], range_config["upper"])
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 形态学处理
        kernel = np.ones((3, 3), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        
        return combined_mask
```

## 推荐的完整优化方案

### 5.1 主要参数修改
```python
# 替换main.py中的固定参数
class AdaptiveRectDetector:
    def __init__(self):
        self.min_area_base = 100      # 基础最小面积
        self.max_area_base = 600      # 基础最大面积  
        self.epsilon_factor = 0.025   # 多边形近似系数
        
    def detect_rectangles(self, img, estimated_distance=2.0):
        """自适应矩形检测"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 自适应二值化
        mean_brightness = np.mean(gray)
        if mean_brightness < 80:
            threshold = 35
        elif mean_brightness > 150:
            threshold = 60  
        else:
            threshold = 46
            
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # 距离自适应面积阈值
        distance_factor = 2.0 / estimated_distance  # 2米为基准
        min_area = int(self.min_area_base * distance_factor * distance_factor)
        max_area = int(self.max_area_base * distance_factor * distance_factor)
        
        # 轮廓检测
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_area < area < max_area:
                epsilon = self.epsilon_factor * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == 4:
                    quads.append((approx, area))
                    
        return quads
```

### 5.2 多尺度检测策略
```python
def multi_scale_detection(img):
    """多尺度矩形检测"""
    results = []
    
    # 不同距离假设下的检测
    for distance in [0.5, 1.0, 1.5, 2.0]:
        detector = AdaptiveRectDetector()
        quads = detector.detect_rectangles(img, distance)
        results.extend(quads)
    
    # 去重和筛选最佳结果
    if results:
        # 按面积排序，选择最大的
        best_quad = max(results, key=lambda x: x[1])
        return [best_quad]
    
    return []
```

## 实施建议

### 6.1 立即可实施的改进
1. **降低面积阈值**：将min_contour_area改为100
2. **增加自适应二值化**：根据图像亮度调整阈值
3. **放宽多边形近似**：epsilon_factor从0.03改为0.025

### 6.2 进阶优化方案
1. **实现距离估算**：基于已知目标尺寸估算距离
2. **多帧融合**：连续帧检测结果的时间滤波
3. **机器学习辅助**：训练分类器提高识别准确率

### 6.3 调试建议
1. **添加调试输出**：显示当前使用的阈值参数
2. **可视化中间结果**：显示二值化图像和轮廓
3. **参数实时调整**：通过串口接收参数调整命令

## 结论

通过以上分析和优化，可以显著提升2米距离内的矩形识别成功率。关键是实现参数的自适应调整，特别是面积阈值和二值化阈值的动态设置。建议优先实施面积阈值降低和自适应二值化两项改进。
